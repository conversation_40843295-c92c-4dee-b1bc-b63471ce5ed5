import re
import html



def clean_text(text: str) -> str:
    text = html.unescape(text)
    text = re.sub(r'<[^>]+>', '', text)  # HTML
    text = re.sub(r'\[?https?://[^\s\]]+\]?', '', text)  # liens avec ou sans []
    text = re.sub(r'http\S+|www\.\S+', '', text)  # autres formes
    text = re.sub(r'\(https?:\/\/.*?\)', '', text)  # lien entre parenthèses
    text = re.sub(r'\s+', ' ', text).strip()
    return text
