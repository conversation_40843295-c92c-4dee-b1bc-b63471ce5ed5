import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.langgraph.workflow import build_newsletter_graph

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware  # <-- 🔥 Ajout ici
from pydantic import BaseModel
from src.tools.revise import revise_newsletter as process  

# 🚀 Création de l'app FastAPI
app = FastAPI()

# 🔄 Charger le graphe LangGraph (sans révision pour l’instant)
graph = build_newsletter_graph(with_revision=False) 

# 🔐 Autoriser CORS pour le client React
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Stockage temporaire en mémoire
last_generated_newsletter = None

class NewsletterRequest(BaseModel):
    theme: str
    description: str

class FeedbackRequest(BaseModel):
    feedback: str  

@app.post("/generate")
def generate_newsletter(data: NewsletterRequest):
    global last_generated_newsletter

    theme = data.theme.strip()
    description = data.description.strip()

    if not theme:
        raise HTTPException(status_code=400, detail="Thème manquant.")

    try:
        result = graph.invoke({
            "theme": theme,
            "description": description
        })

        newsletter = result.get("newsletter")
        if not newsletter:
            raise HTTPException(status_code=500, detail="Newsletter vide générée.")

        last_generated_newsletter = newsletter
        return {"newsletter": newsletter}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur dans le graphe : {str(e)}")


@app.post("/revise")
def revise_newsletter(data: FeedbackRequest):
    global last_generated_newsletter

    if not last_generated_newsletter:
        raise HTTPException(status_code=400, detail="Aucune newsletter générée auparavant.")

    revised = process(last_generated_newsletter, data.feedback)
    return {"revised_newsletter": revised}

@app.get("/")
def read_root():
    return {"message": "🚀 API de génération de newsletter opérationnelle"}

































# import sys
# import os
# os.environ["TORCH_CLASS_EXTENSION_ALLOW_LIST"] = "True"
# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# import asyncio
# try:
#     asyncio.get_running_loop()
# except RuntimeError:
#     asyncio.set_event_loop(asyncio.new_event_loop())

# import streamlit as st
# import streamlit.components.v1 as components
# from src.agent.scraping import scrape_content
# from models.Llama3_summary import summarize
# from src.agent.nwesletter_generator import generate_newsletter_from_summary

# st.title("🔖 Générateur de Newsletter ")

# theme = st.text_input("🌟 Thème de la newsletter", placeholder="ex : Votre thème ")
# description = st.text_area("📟 Description courte", placeholder="Décrivez brièvement l'objectif ou le contenu...")

# if st.button("✨ le contenu"):
#     if not theme.strip():
#         st.warning("⚠️ Veuillez entrer un thème.")
#     else:
#         st.subheader("🔍 Résultats du scraping")
#         st.markdown(f"**🌟 Thème choisi :** `{theme}`")
#         st.markdown(f"**📟 Description :** `{description}`")

#         with st.spinner("🔄 Scraping en cours..."):
#             scraped_data = scrape_content(theme, description)

#         if scraped_data:
#             for item in scraped_data:
#                 st.markdown(f"- {item}")

#             full_text = "\n".join(scraped_data)

#             st.subheader("🧬 Résumé")
#             with st.spinner("✍️ Résumé en cours..."):
#                 summary = summarize(full_text)
#                 st.success("Résumé généré avec succès ✅")
#                 st.markdown(f"**📝 Résumé final :**\n\n> {summary}")

#             st.subheader("💌 Newsletter")
#             with st.spinner("🧠 Rédaction de la newsletter..."):
#                 newsletter = generate_newsletter_from_summary(summary, theme=theme)
#                 st.success("Newsletter prête ✅")
#                 st.text_area("📄 Newsletter générée", value=newsletter, height=300, key="newsletter_box")
#                 st.session_state["latest_newsletter"] = newsletter

#                 # ✅ Copier sans rechargement
#                 copy_button = """
#                 <script>
#                 function copyToClipboard() {
#                     const textarea = window.parent.document.querySelector('textarea[data-testid="stTextArea"]');
#                     if (textarea) {
#                         textarea.select();
#                         document.execCommand('copy');
#                         alert("✅ Newsletter copiée !");
#                     } else {
#                         alert("❌ Échec de la copie !");
#                     }
#                 }
#                 </script>
#                 <button onclick="copyToClipboard()">📋 Copier la newsletter</button>
#                 """
#                 components.html(copy_button, height=40)

#             # 📝 Remarques utilisateur
#             st.markdown("### ✏️ Vos remarques")
#             feedback = st.text_area("Que souhaitez-vous modifier ou améliorer dans la newsletter ?", key="feedback_input")

#             if st.button("🔁 Réviser la newsletter"):
#                 if "latest_newsletter" not in st.session_state or not st.session_state.latest_newsletter.strip():
#                     st.warning("❌ La newsletter originale est manquante. Veuillez la générer d'abord.")
#                 elif not feedback.strip():
#                     st.warning("✏️ Merci d'écrire une remarque avant de relancer.")
#                 else:
#                     with st.spinner("🔧 Mise à jour de la newsletter selon vos instructions..."):
#                         prompt_feedback = f"""Tu es un assistant de rédaction.
#                         Voici une newsletter existante : 
#                         --- 
#                         {st.session_state['latest_newsletter']} 
#                         ---
#                         Voici la remarque de l'utilisateur : "{feedback}"
#                         Corrige ou adapte la newsletter en conséquence, en gardant un style clair et professionnel.
#                         Réponds uniquement avec la version révisée.
#                         """
#                         revised = generate_newsletter_from_summary(prompt_feedback)
#                         st.success("✅ Newsletter mise à jour 🎯")
#                         st.code(revised, language="markdown")

#         else:
#             st.info("Aucun contenu pertinent trouvé.")



































# import sys
# import os
# os.environ["TORCH_CLASS_EXTENSION_ALLOW_LIST"] = "True"
# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# import asyncio
# try:
#     asyncio.get_running_loop()
# except RuntimeError:
#     asyncio.set_event_loop(asyncio.new_event_loop())

# import streamlit as st
# from src.agent.scraping import scrape_content
# from models.Llama3_summary import summarize
# from src.agent.nwesletter_generator import generate_newsletter_from_summary



# st.title("🔖 Générateur de Newsletter ")

# theme = st.text_input("🌟 Thème de la newsletter", placeholder="ex : Votre thème ")
# description = st.text_area("📟 Description courte", placeholder="Décrivez brièvement l'objectif ou le contenu...")

# if st.button("✨ le contenu"):
#     if not theme.strip():
#         st.warning("⚠️ Veuillez entrer un thème.")
#     else:
#         st.subheader("🔍 Résultats du scraping")
#         st.markdown(f"**🌟 Thème choisi :** `{theme}`")
#         st.markdown(f"**📟 Description :** `{description}`")

#         with st.spinner("🔄 Scraping en cours..."):
#             scraped_data = scrape_content(theme, description)

#         if scraped_data:
#             for item in scraped_data:
#                 st.markdown(f"- {item}")

#             full_text = "\n".join(scraped_data)

#             st.subheader("🧬 Résumé  ")
#             with st.spinner("✍️ Résumé en cours..."):
#                 summary = summarize(full_text)
#                 st.success("Résumé généré avec succès ✅")
#                 st.markdown(f"**📝 Résumé final :**\n\n> {summary}")

#             st.subheader("💌 Newsletter ")
#             with st.spinner("🧠 Rédaction de la newsletter..."):
#                 newsletter = generate_newsletter_from_summary(summary, theme=theme)
#                 st.success("Newsletter prête ✅")
#                 st.markdown(f"```markdown\n{newsletter}\n```") 
#                 st.session_state["latest_newsletter"] = newsletter

#             # st.success("Newsletter prête ✅")
#             # st.code(newsletter, language="markdown")

#             st.button("📋 Copier la newsletter", on_click=st.toast, args=("✅ Copiée dans le presse-papiers (Ctrl+C)",))


#             st.markdown("### ✏️ Vos remarques")
#             feedback = st.text_area("Que souhaitez-vous modifier ou améliorer dans la newsletter ?", key="feedback_input")

#             if st.button("🔁 Réviser la newsletter"):
#                 if feedback.strip():
#                     with st.spinner("🔧 Mise à jour de la newsletter selon vos instructions..."):
#                         prompt_feedback = f"""Tu es un assistant de rédaction.
#                         Voici une newsletter existante : 
#                         --- 
#                         {st.session_state['latest_newsletter']} 
#                         ---
#                         Voici la remarque de l'utilisateur : "{feedback}"
#                         Corrige ou adapte la newsletter en conséquence, en gardant un style clair et professionnel.
#                         Réponds uniquement avec la version révisée.
#                         """
#                         revised = generate_newsletter_from_summary(prompt_feedback)
#                         st.success("Newsletter mise à jour 🎯")
#                         st.code(revised, language="markdown")
#                 else:
#                     st.warning("✏️ Merci d'écrire une remarque avant de relancer.")       
#         else:
#             st.info("Aucun contenu pertinent trouvé.")
