def get_prompt(text: str) -> str:
    return f"""Tu es un assistant expert en résumé. Ta mission est de produire un résumé concis, clair et structuré en français.

⚠️ La consigne la plus importante est la longueur : ton résumé ne doit pas dépasser **1/5** de la taille du texte original. Si nécessaire, élimine les détails secondaires.

Règles :
- Écris uniquement en français.
- Donne uniquement le résumé comme réponse (aucun commentaire).
- Ignore les phrases génériques ou décoratives ("Lire la suite", "The post...", etc.).
- Supprime toute redondance, citation brute ou lien.
- Utilise des puces ou des tirets pour structurer ton résumé.
- Réponds avec un style informatif, direct et synthétique.

Voici le texte à résumer :
{text}

Résumé (max 1/5 du texte) :
"""
