"use client";

import { useState } from "react";

export default function FeedbackForm({ onRevise }: { onRevise: (feedback: string) => Promise<void> }) {
  const [feedback, setFeedback] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleRevise = async () => {
    setIsLoading(true);
    await onRevise(feedback);
    setIsLoading(false);
  };

  return (
    <div className="mt-4">
      <textarea
        placeholder="Entrer vos remarques ici..."
        value={feedback}
        onChange={(e) => setFeedback(e.target.value)}
        rows={3}
        className="w-full border p-3 rounded-md resize-none mb-2 text-gray-900 placeholder-gray-500"
      />
      <button
        onClick={handleRevise}
        disabled={isLoading}
        className={`w-full py-2 rounded-md transition ${
          isLoading
            ? "bg-green-400 cursor-not-allowed"
            : "bg-green-600 hover:bg-green-700 text-white"
        }`}
      >
        {isLoading ? "Modification en cours..." : "Modifier"}
      </button>
    </div>
  );
}
