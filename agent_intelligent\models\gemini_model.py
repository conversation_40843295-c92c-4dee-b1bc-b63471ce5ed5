import os
from dotenv import load_dotenv
import google.generativeai as genai

# 🔐 Charger la clé depuis .env
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if not GEMINI_API_KEY:
    raise RuntimeError("❌ Clé API Gemini manquante. Ajoute-la dans .env comme GEMINI_API_KEY=...")

# 🔧 Configuration Gemini
genai.configure(api_key=GEMINI_API_KEY)

# 📦 Modèle exporté
gemini__model = genai.GenerativeModel("gemini-2.0-flash")
