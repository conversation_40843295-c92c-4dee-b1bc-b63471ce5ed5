# Agent Intelligent pour la Génération Automatisée de Newsletters

## Description
Ce projet propose un workflow automatisé complet pour la génération de newsletters à partir de diverses sources d'information (sites web, flux RSS, réseaux sociaux...). Il s'appuie sur des technologies d'IA, de traitement du langage naturel (TALN) et des outils de scraping, de résumé automatique et de génération HTML.

---

## Architecture du Projet

### 1. **Backend (agent_intelligent)**
- **Orchestration du workflow** : Utilisation de LangGraph pour enchaîner les étapes (scraping → résumé → génération → révision optionnelle).
- **Modules principaux** :
  - `models/` : Intégration de modèles LLM (Llama3 via Groq, Gemini via Google).
  - `prompts/` : Prompts spécialisés pour chaque étape (résumé, génération, révision).
  - `src/langgraph/workflow.py` : Construction du pipeline automatisé.
  - `src/tools/` : Outils pour scraping (web, RSS, Twitter), résumé, génération, révision, prétraitement.
  - `src/interfaces/backend/api.py` : API FastAPI exposant les endpoints `/generate` (génération) et `/revise` (révision via feedback utilisateur).
- **Fonctionnement** :
  - Scraping intelligent de contenus (web, RSS, Twitter) selon un thème et une description.
  - Résumé automatique via Llama3 (Groq).
  - Génération de la newsletter via Gemini (Google).
  - Révision possible selon feedback utilisateur.
  - Prompts personnalisés pour chaque étape, garantissant la qualité rédactionnelle.

### 2. **Frontend (Next.js)**
- **Structure** :
  - `src/app/` : Pages principales (`page.tsx`), layout, styles globaux.
  - `src/components/` : Composants réutilisables (formulaire, affichage newsletter, feedback, bouton copie).
  - `public/` : Assets graphiques.
- **Fonctionnalités** :
  - Saisie du thème et de la description.
  - Affichage de la newsletter générée.
  - Formulaire de feedback pour révision.
  - Intégration avec l'API backend pour génération/révision en temps réel.

### 3. **Technologies clés**
- **Backend** : Python, FastAPI, LangChain, LangGraph, Llama3 (Groq), Gemini (Google), BeautifulSoup, feedparser, snscrape.
- **Frontend** : Next.js, React, TypeScript, CSS.
- **Interopérabilité** : API REST entre frontend et backend.

### 4. **Pipeline automatisé**
1. **Collecte** : Scraping multi-sources (web, RSS, Twitter).
2. **Prétraitement** : Nettoyage, normalisation, détection de langue.
3. **Résumé** : Modèle Llama3 via Groq.
4. **Génération** : Modèle Gemini via prompt structuré.
5. **Révision** : Sur demande, via prompt enrichi par le feedback utilisateur.
6. **Diffusion** : (À intégrer) vers plateformes emailing.

---

## Organisation des dossiers

```
agent_intelligent/
  models/                # Modèles LLM (Groq, Gemini)
  prompts/               # Prompts pour chaque étape
  src/
    langgraph/           # Orchestration du workflow
    tools/               # Outils : scraping, résumé, génération, révision
    interfaces/
      backend/           # API FastAPI
      frontend/          # Application Next.js (voir README dédié)
```

---

## Installation & Lancement

### **1. Backend (API FastAPI)**

**Prérequis** : Python 3.10+, clés API Groq et Gemini

```bash
cd agent_intelligent/src/interfaces/backend
pip install -r ../../../requirements.txt
# Variables d'environnement à définir :
# GROQ_API_KEY=...
# GEMINI_API_KEY=...
uvicorn api:app --reload
```

### **2. Frontend (Next.js)**

```bash
cd agent_intelligent/src/interfaces/frontend
npm install
npm run dev
```

L'interface sera accessible sur [http://localhost:3000](http://localhost:3000)

---

## Variables d'environnement nécessaires
- `GROQ_API_KEY` : pour l'accès au modèle Llama3 (Groq)
- `GEMINI_API_KEY` : pour l'accès au modèle Gemini (Google)

---




