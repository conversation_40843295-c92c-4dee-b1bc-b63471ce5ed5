from langchain.agents import initialize_agent, AgentType
from langchain.chat_models import ChatOpenAI  # ou autre LLM
from src.tools.scraping_tool import scrape_news
from src.tools.summary_tool import summarize_text
from src.tools.generation_tool import generate_newsletter
from src.tools.revise_tool import revise_newsletter_tool
from models.chat_groq import ChatGroq





# Liste des outils à donner à l’agent
tools = [scrape_news, summarize_text, generate_newsletter, revise_newsletter_tool]

# 🔧 LLM utilisé par l’agent (OpenAI ici, tu peux mettre Groq si tu veux)
llm = ChatGroq(model="llama3-8b-8192", temperature=0.7)

# 🧠 Initialisation de l’agent avec outils et LLM
agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.OPENAI_FUNCTIONS,  # ou AgentType.ZERO_SHOT_REACT_DESCRIPTION
    verbose=True
)
