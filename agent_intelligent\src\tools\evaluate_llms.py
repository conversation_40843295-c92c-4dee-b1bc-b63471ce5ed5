from transformers import pipeline
from rouge_score import rouge_scorer
import textstat
import pandas as pd
import concurrent.futures
import evaluate

# Initialisation des deux LLMs
models = {
    "BART": pipeline("summarization", model="facebook/bart-large-cnn"),
    "T5": pipeline("summarization", model="t5-large", tokenizer="t5-large")
}

scorer = rouge_scorer.RougeScorer(['rouge1','rouge2','rougeL'], use_stemmer=True)
bertscorer = evaluate.load("bertscore")
meteor = evaluate.load("meteor")

def summarize_in_chunks(model, content, chunk_size=800, partial_max_len=60, final_max_len=100):
    if not content or len(content.strip()) < 20:
        return "⛔ Contenu insuffisant."

    chunks = [content[i:i+chunk_size] for i in range(0, len(content), chunk_size)]

    def summarize_chunk(chunk):
        prompt = f"Résume ce texte :\n{chunk}"
        try:
            result = model(prompt, max_length=partial_max_len, min_length=20, do_sample=False)
            return result[0]['summary_text'] if result else ""
        except Exception as e:
            return f"[Erreur chunk : {e}]"

    with concurrent.futures.ThreadPoolExecutor() as executor:
        partials = list(executor.map(summarize_chunk, chunks))

    combined = " ".join(partials)
    sub_partials = []

    if len(combined) > 3000:
        sub_chunks = [combined[i:i+1000] for i in range(0, len(combined), 1000)]
        with concurrent.futures.ThreadPoolExecutor() as executor:
            sub_partials = list(executor.map(summarize_chunk, sub_chunks))
        combined = " ".join(sub_partials)

    try:
        final_prompt = f"Synthétise ce contenu :\n{combined}"
        final_result = model(final_prompt, max_length=final_max_len, min_length=40, do_sample=False)
        return final_result[0]['summary_text'] if final_result else combined
    except Exception as e:
        return f"[Erreur résumé final : {e}]"

def evaluate_models(documents):
    results = []

    for i, text in enumerate(documents):
        for name, model in models.items():
            summary = summarize_in_chunks(model, text)
            readability = textstat.flesch_reading_ease(summary)
            rouge = scorer.score(text, summary)
            bert = bertscorer.compute(predictions=[summary], references=[text], lang="fr")
            meteor_score = meteor.compute(predictions=[summary], references=[text])

            compression_ratio = round(len(summary.split()) / len(text.split()), 2) if len(text.split()) else 0

            results.append({
                "Document": f"Doc {i+1}",
                "LLM": name,
                "Résumé": summary,
                "ROUGE-1": round(rouge['rouge1'].fmeasure, 3),
                "ROUGE-2": round(rouge['rouge2'].fmeasure, 3),
                "ROUGE-L": round(rouge['rougeL'].fmeasure, 3),
                "BERTScore (F1)": round(bert['f1'][0], 3),
                "METEOR": round(meteor_score['meteor'], 3),
                "Lisibilité": round(readability, 1),
                "Ratio compression": compression_ratio
            })

    return pd.DataFrame(results)
