"use client";

import { useState } from "react";
import axios from "axios";
import Form from "@/components/Form";
import NewsletterDisplay from "@/components/NewsletterDisplay";
import FeedbackForm from "@/components/FeedbackForm";
import CopyButton from "@/components/CopyButton";

export default function Home() {
  const [newsletter, setNewsletter] = useState("");
  const [revised, setRevised] = useState("");
  const [copied, setCopied] = useState(false);

  const generateNewsletter = async (theme: string, description: string) => {
    try {
      const res = await axios.post("http://localhost:8000/generate", {
        theme,
        description,
      });
      setNewsletter(res.data.newsletter);
      setRevised("");
      setCopied(false);
    } catch {
      alert("Erreur lors de la génération.");
    }
  };

  const reviseNewsletter = async (feedback: string) => {
    try {
      const res = await axios.post("http://localhost:8000/revise", {
        feedback,
      });
      setRevised(res.data.revised_newsletter);
      setNewsletter("");
      setCopied(false);
    } catch {
      alert("Erreur lors de la révision.");
    }
  };

  const content = revised || newsletter;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-700 via-blue-600 to-indigo-800 p-8 flex flex-col items-center">
      
      {/* 💡 ZONE 1 : Image + Form side by side */}
      <div className="flex flex-col md:flex-row bg-white rounded-2xl shadow-lg overflow-hidden w-full max-w-6xl">
        
        {/* Illustration à gauche */}
        <div className="w-full md:w-1/2 bg-gradient-to-b from-purple-800 to-blue-700 text-white flex flex-col justify-center items-center p-8">
          <img
            src="/nwesletter.jpg"
            alt="AI Illustration"
            className="w-64 h-64 md:w-80 md:h-80 border-4 border-white rounded-xl shadow-lg mb-4"
          />
          <h1 className="text-3xl font-bold text-center">Hello! 👋</h1>
          <p className="text-xl mt-1 text-center text-purple-100">
            Générez vos newsletters automatiquement avec l’IA ✨
          </p>
        </div>

        {/* Formulaire à droite */}
        <div className="w-full md:w-1/2 p-8">
          <h2 className="text-2xl font-semibold text-purple-800 text-center mb-6">
            Générateur de Newsletter
          </h2>
          <Form onGenerate={generateNewsletter} />
        </div>
      </div>

      {/* 📰 ZONE 2 : Newsletter + Feedback */}
      {content && (
        <div className="w-full max-w-6xl bg-white mt-8 p-6 rounded-xl shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Newsletter générée :</h3>

          <div className="bg-gray-50 border border-gray-300 rounded-md p-4 text-gray-800 whitespace-pre-wrap text-sm leading-relaxed">
            {content}
          </div>

          {!revised && (
            <div className="mt-4">
              <FeedbackForm onRevise={reviseNewsletter} />
            </div>
          )}

          <div className="mt-4">
            <CopyButton content={content} />
          </div>
        </div>
      )}
    </div>
  );
}





























// "use client";

// import { useState } from 'react';
// import axios from 'axios';

// export default function Home() {
//   const [theme, setTheme] = useState('');
//   const [description, setDescription] = useState('');
//   const [newsletter, setNewsletter] = useState('');
//   const [feedback, setFeedback] = useState('');
//   const [revised, setRevised] = useState('');
//   const [copied, setCopied] = useState(false);

//   const generateNewsletter = async () => {
//     try {
//       const res = await axios.post('http://localhost:8000/generate', { theme, description });
//       setNewsletter(res.data.newsletter);
//       setRevised('');
//       setCopied(false);
//     } catch (err) {
//       alert('Erreur lors de la génération.');
//     }
//   };

//   const reviseNewsletter = async () => {
//     try {
//       const res = await axios.post('http://localhost:8000/revise', { feedback });
//       setRevised(res.data.revised_newsletter);
//       setNewsletter('');
//       setCopied(false);
//     } catch (err) {
//       alert('Erreur lors de la révision.');
//     }
//   };

//   const copyToClipboard = () => {
//     const content = revised || newsletter;
//     navigator.clipboard.writeText(content);
//     setCopied(true);
//   };

//   return (
//     <div style={{ maxWidth: '800px', margin: '0 auto', padding: '2rem' }}>
//       <h1>📰 Générateur de Newsletter</h1>

//       <input
//         type="text"
//         placeholder="Thème"
//         value={theme}
//         onChange={(e) => setTheme(e.target.value)}
//         style={{ width: '100%', marginBottom: '10px' }}
//       />

//       <textarea
//         placeholder="Description"
//         value={description}
//         onChange={(e) => setDescription(e.target.value)}
//         style={{ width: '100%', height: '80px', marginBottom: '10px' }}
//       />

//       <button onClick={generateNewsletter}>Générer</button>

//       {newsletter && (
//         <div style={{ marginTop: '2rem' }}>
//           <h2>Newsletter Générée :</h2>
//           <p>{newsletter}</p>

//           <textarea
//             placeholder="Vos remarques..."
//             value={feedback}
//             onChange={(e) => setFeedback(e.target.value)}
//             style={{ width: '100%', height: '60px', marginTop: '10px' }}
//           />
//           <button onClick={reviseNewsletter}>Modifier</button>
//         </div>
//       )}

//       {revised && (
//         <div style={{ marginTop: '2rem' }}>
//           <h2>Newsletter Révisée :</h2>
//           <p>{revised}</p>
//         </div>
//       )}

//       {(newsletter || revised) && (
//         <button onClick={copyToClipboard} style={{ marginTop: '20px' }}>
//           {copied ? '✅ Copié !' : 'Copier la Newsletter'}
//         </button>
//       )}
//     </div>
//   );
// }
